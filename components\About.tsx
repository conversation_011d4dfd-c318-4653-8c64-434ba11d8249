'use client'

import { motion } from 'framer-motion'
import { Globe, Zap, Users, Clock } from 'lucide-react'

const About = () => {
  const features = [
    {
      icon: Globe,
      title: 'Global Impact',
      description: 'Operating across 25+ countries with humanitarian projects reaching underserved communities worldwide.',
      gradient: 'from-teal to-teal-dark'
    },
    {
      icon: Zap,
      title: 'Innovative Solutions',
      description: 'Pioneering project management approaches that create sustainable and lasting humanitarian impact.',
      gradient: 'from-gold to-gold-dark'
    },
    {
      icon: Users,
      title: 'Community Partnerships',
      description: 'Collaborating with local communities, NGOs, and governments to ensure culturally appropriate solutions.',
      gradient: 'from-teal-light to-gold'
    },
    {
      icon: Clock,
      title: 'Proven Excellence',
      description: 'Over 15 years of successful humanitarian project delivery with measurable community impact.',
      gradient: 'from-gold-light to-teal'
    }
  ]

  return (
    <section id="about" className="py-20 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-white via-teal-50 to-gold-50">
        <div className="absolute top-0 left-0 w-full h-full opacity-5">
          <div className="absolute top-20 left-20 w-64 h-64 bg-teal rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-20 w-96 h-96 bg-gold rounded-full blur-3xl"></div>
        </div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            Our Mission
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-teal to-gold mx-auto mb-6"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Founded on compassion and driven by excellence, Kaiteur Equity Partners
            has evolved into a leading humanitarian project management company, transforming lives across the globe.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Story Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <div className="glass p-8 rounded-2xl hover-lift">
              <h3 className="text-2xl font-semibold text-teal mb-4">Founded on Compassion</h3>
              <p className="text-gray-600 mb-4 leading-relaxed">
                Kaiteur Equity Partners was established with a vision to create sustainable humanitarian
                impact through expert project management and strategic community partnerships.
              </p>
              <p className="text-gray-600 mb-4 leading-relaxed">
                Starting as a focused humanitarian initiative, we&apos;ve grown into a comprehensive
                project management company specializing in emergency relief, development, and community empowerment.
              </p>
              <p className="text-gray-600 leading-relaxed">
                Our journey has been defined by successful project delivery, meaningful partnerships,
                and a commitment to sustainable development that transforms communities worldwide.
              </p>
            </div>
          </motion.div>

          {/* Features Grid */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="grid grid-cols-1 sm:grid-cols-2 gap-6"
          >
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                viewport={{ once: true }}
                className="glass p-6 rounded-xl hover-lift group"
                whileHover={{ scale: 1.02 }}
              >
                <div className={`w-12 h-12 rounded-lg bg-gradient-to-br ${feature.gradient} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-gray-dark mb-2">{feature.title}</h4>
                <p className="text-gray-600 text-sm leading-relaxed">{feature.description}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Values Section */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <h3 className="text-3xl font-bold text-gray-800 mb-8">Our Core Values</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              { title: 'Compassion', description: 'Leading with empathy and understanding for those we serve' },
              { title: 'Excellence', description: 'Delivering the highest quality humanitarian project management' },
              { title: 'Collaboration', description: 'Building partnerships that amplify our collective impact' },
              { title: 'Sustainability', description: 'Creating lasting solutions that empower communities' }
            ].map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                viewport={{ once: true }}
                className="bg-gradient-to-r from-teal to-gold p-6 rounded-xl hover-lift shadow-xl"
                whileHover={{ y: -5 }}
              >
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-white/20 backdrop-blur-sm border-2 border-white/30 flex items-center justify-center">
                  <span className="text-white font-bold text-xl">{value.title[0]}</span>
                </div>
                <h4 className="text-xl font-semibold text-white mb-2">{value.title}</h4>
                <p className="text-white/90 text-sm">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Leadership Team Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16 mt-20"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">Our Leadership Team</h2>
          <div className="w-20 h-1 bg-gradient-to-r from-orange-500 to-teal-600 mx-auto mb-6"></div>
          <p className="text-lg text-gray-700 max-w-2xl mx-auto">
            The visionary leaders guiding our humanitarian mission forward.
          </p>
        </motion.div>

        {/* Founders Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {/* Ien Chapman */}
          <motion.div
            className="bg-gradient-to-r from-teal to-gold p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            whileHover={{ y: -5 }}
          >
            <h3 className="text-2xl font-bold text-white mb-2">Ien Chapman</h3>
            <p className="text-white/90 font-medium text-lg mb-4">Founder & CEO</p>
            <p className="text-white/80">Veteran | Humanitarian Leader | Visionary</p>
            <p className="text-white/80 mt-4 text-sm">
              A decorated veteran with a passion for serving others, Ien brings strategic leadership and a commitment to excellence in all our humanitarian initiatives.
            </p>
          </motion.div>

          {/* Arlene Chapman */}
          <motion.div
            className="bg-gradient-to-r from-teal to-gold p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            whileHover={{ y: -5 }}
          >
            <h3 className="text-2xl font-bold text-white mb-2">Arlene Chapman</h3>
            <p className="text-white/90 font-medium text-lg mb-4">Co-Founder & COO</p>
            <p className="text-white/80">Community Leader | Project Strategist | Philanthropist</p>
            <p className="text-white/80 mt-4 text-sm">
              With a heart for service and a mind for strategic project management, Arlene ensures our operations align with our mission to create lasting, positive change in communities worldwide.
            </p>
          </motion.div>
        </div>

        {/* C-Team Section */}
        <motion.div
          className="mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.1 }}
          viewport={{ once: true }}
        >
          <h3 className="text-2xl font-bold text-gray-800 mb-8 text-center">Executive Leadership</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              { name: 'Wayne Robinson', role: 'Chief Project Officer' },
              { name: 'Xavier Grier', role: 'Chief Community Engagement Officer' },
              { name: 'Johnny Walker', role: 'Chief Human Resources Officer' },
              { name: 'Joel Rodriguez', role: 'Chief Technology Officer' }
            ].map((member, index) => (
              <motion.div
                key={member.name}
                className="bg-gradient-to-r from-teal to-gold p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 text-center"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 * index }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
              >
                <h4 className="text-lg font-semibold text-white mb-1">{member.name}</h4>
                <p className="text-white/90 text-sm font-medium">{member.role}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Directors Section */}
        <motion.div
          className="mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <h3 className="text-2xl font-bold text-gray-800 mb-8 text-center">Directors</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-3xl mx-auto">
            {[
              { name: 'Mary Young', role: 'Sr. Program Director' },
              { name: 'Alfred Strachan', role: 'Jr. Program Director' }
            ].map((member, index) => (
              <motion.div
                key={member.name}
                className="bg-gradient-to-r from-teal to-gold p-4 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 text-center"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 * index }}
                viewport={{ once: true }}
                whileHover={{ y: -3 }}
              >
                <h4 className="text-md font-semibold text-white">{member.name}</h4>
                <p className="text-white/90 text-xs font-medium">{member.role}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default About
