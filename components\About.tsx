'use client'

import { motion } from 'framer-motion'
import { Globe, Zap, Users, Clock } from 'lucide-react'

const About = () => {
  const features = [
    {
      icon: Globe,
      title: 'Global Impact',
      description: 'Operating across 25+ countries with humanitarian projects reaching underserved communities worldwide.',
      gradient: 'from-teal to-teal-dark'
    },
    {
      icon: Zap,
      title: 'Innovative Solutions',
      description: 'Pioneering project management approaches that create sustainable and lasting humanitarian impact.',
      gradient: 'from-gold to-gold-dark'
    },
    {
      icon: Users,
      title: 'Community Partnerships',
      description: 'Collaborating with local communities, NGOs, and governments to ensure culturally appropriate solutions.',
      gradient: 'from-teal-light to-gold'
    },
    {
      icon: Clock,
      title: 'Proven Excellence',
      description: 'Over 15 years of successful humanitarian project delivery with measurable community impact.',
      gradient: 'from-gold-light to-teal'
    }
  ]

  return (
    <section id="about" className="py-32 relative overflow-hidden bg-gray-50">
      {/* Modern Background Design */}
      <div className="absolute inset-0">
        {/* Layered Background */}
        <div className="absolute inset-0 bg-gradient-to-b from-white via-gray-50 to-white"></div>

        {/* Geometric Patterns */}
        <div className="absolute top-0 left-0 w-full h-32 bg-gradient-to-b from-teal-500/5 to-transparent"></div>
        <div className="absolute bottom-0 right-0 w-full h-32 bg-gradient-to-t from-gold-400/5 to-transparent"></div>

        {/* Subtle Texture */}
        <div className="absolute inset-0 opacity-[0.015]" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, rgba(20, 184, 166, 0.1) 0%, transparent 50%),
                           radial-gradient(circle at 75% 75%, rgba(245, 158, 11, 0.1) 0%, transparent 50%)`
        }}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header - Side by Side Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-24">
          {/* Left Side - Title */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="inline-flex items-center px-4 py-2 bg-teal-100 text-teal-800 rounded-full text-sm font-semibold mb-6">
              <Heart className="w-4 h-4 mr-2" />
              About Kaiteur Equity Partners
            </div>
            <h2 className="text-5xl md:text-6xl font-black text-gray-900 leading-tight mb-6">
              Humanitarian
              <span className="block bg-gradient-to-r from-teal-600 to-gold-500 bg-clip-text text-transparent">
                Excellence
              </span>
            </h2>
            <div className="w-20 h-2 bg-gradient-to-r from-teal-500 to-gold-500 rounded-full mb-6"></div>
          </motion.div>

          {/* Right Side - Description */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <p className="text-xl text-gray-700 leading-relaxed">
              Founded on compassion and driven by excellence, Kaiteur Equity Partners
              has evolved into a leading humanitarian project management company.
            </p>
            <p className="text-lg text-gray-600 leading-relaxed">
              We specialize in transforming communities through strategic project delivery,
              sustainable development initiatives, and meaningful partnerships that create
              lasting positive impact across the globe.
            </p>
            <div className="flex items-center space-x-4 pt-4">
              <div className="flex -space-x-2">
                <div className="w-12 h-12 bg-gradient-to-br from-teal-500 to-teal-600 rounded-full border-4 border-white flex items-center justify-center">
                  <span className="text-white font-bold text-sm">15+</span>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-gold-500 to-gold-600 rounded-full border-4 border-white flex items-center justify-center">
                  <span className="text-white font-bold text-sm">25+</span>
                </div>
              </div>
              <div>
                <p className="text-sm font-semibold text-gray-900">15+ Years Experience</p>
                <p className="text-sm text-gray-600">25+ Countries Served</p>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Features Section - Horizontal Cards */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mb-24"
        >
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Why Choose Kaiteur Equity Partners</h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Our comprehensive approach to humanitarian project management sets us apart in delivering sustainable impact.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                viewport={{ once: true }}
                className="bg-white border-2 border-gray-100 p-8 rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-300 group hover:border-teal-200"
                whileHover={{ y: -5 }}
              >
                <div className="flex items-start space-x-6">
                  <div className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${feature.gradient} flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300`}>
                    <feature.icon className="w-8 h-8 text-white" />
                  </div>
                  <div className="flex-1">
                    <h4 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h4>
                    <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Values Section */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <h3 className="text-3xl font-bold text-gray-800 mb-8">Our Core Values</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              { title: 'Compassion', description: 'Leading with empathy and understanding for those we serve' },
              { title: 'Excellence', description: 'Delivering the highest quality humanitarian project management' },
              { title: 'Collaboration', description: 'Building partnerships that amplify our collective impact' },
              { title: 'Sustainability', description: 'Creating lasting solutions that empower communities' }
            ].map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                viewport={{ once: true }}
                className="bg-gradient-to-br from-teal-500 to-gold-500 p-8 rounded-3xl hover-lift shadow-2xl border-4 border-white"
                whileHover={{ y: -5 }}
              >
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-white/20 backdrop-blur-sm border-2 border-white/30 flex items-center justify-center">
                  <span className="text-white font-bold text-xl">{value.title[0]}</span>
                </div>
                <h4 className="text-xl font-semibold text-white mb-2">{value.title}</h4>
                <p className="text-white/90 text-sm">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Leadership Team Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16 mt-20"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">Our Leadership Team</h2>
          <div className="w-20 h-1 bg-gradient-to-r from-teal-500 to-gold-500 mx-auto mb-6"></div>
          <p className="text-lg text-gray-700 max-w-2xl mx-auto">
            The visionary leaders guiding our humanitarian mission forward.
          </p>
        </motion.div>

        {/* Founders Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {/* Ien Chapman */}
          <motion.div
            className="bg-gradient-to-br from-teal-500 to-gold-500 p-8 rounded-3xl shadow-2xl hover:shadow-3xl transition-all duration-300 border-4 border-white"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            whileHover={{ y: -5 }}
          >
            <h3 className="text-2xl font-bold text-white mb-2">Ien Chapman</h3>
            <p className="text-white/90 font-medium text-lg mb-4">Founder & CEO</p>
            <p className="text-white/80">Veteran | Humanitarian Leader | Visionary</p>
            <p className="text-white/80 mt-4 text-sm">
              A decorated veteran with a passion for serving others, Ien brings strategic leadership and a commitment to excellence in all our humanitarian initiatives.
            </p>
          </motion.div>

          {/* Arlene Chapman */}
          <motion.div
            className="bg-gradient-to-br from-teal-500 to-gold-500 p-8 rounded-3xl shadow-2xl hover:shadow-3xl transition-all duration-300 border-4 border-white"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            whileHover={{ y: -5 }}
          >
            <h3 className="text-2xl font-bold text-white mb-2">Arlene Chapman</h3>
            <p className="text-white/90 font-medium text-lg mb-4">Co-Founder & COO</p>
            <p className="text-white/80">Community Leader | Project Strategist | Philanthropist</p>
            <p className="text-white/80 mt-4 text-sm">
              With a heart for service and a mind for strategic project management, Arlene ensures our operations align with our mission to create lasting, positive change in communities worldwide.
            </p>
          </motion.div>
        </div>

        {/* C-Team Section */}
        <motion.div
          className="mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.1 }}
          viewport={{ once: true }}
        >
          <h3 className="text-2xl font-bold text-gray-800 mb-8 text-center">Executive Leadership</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              { name: 'Wayne Robinson', role: 'Chief Project Officer' },
              { name: 'Xavier Grier', role: 'Chief Community Engagement Officer' },
              { name: 'Johnny Walker', role: 'Chief Human Resources Officer' },
              { name: 'Joel Rodriguez', role: 'Chief Technology Officer' }
            ].map((member, index) => (
              <motion.div
                key={member.name}
                className="bg-gradient-to-br from-teal-500 to-gold-500 p-6 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 text-center border-3 border-white"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 * index }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
              >
                <h4 className="text-lg font-semibold text-white mb-1">{member.name}</h4>
                <p className="text-white/90 text-sm font-medium">{member.role}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Directors Section */}
        <motion.div
          className="mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <h3 className="text-2xl font-bold text-gray-800 mb-8 text-center">Directors</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-3xl mx-auto">
            {[
              { name: 'Mary Young', role: 'Sr. Program Director' },
              { name: 'Alfred Strachan', role: 'Jr. Program Director' }
            ].map((member, index) => (
              <motion.div
                key={member.name}
                className="bg-gradient-to-br from-teal-500 to-gold-500 p-4 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 text-center border-3 border-white"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 * index }}
                viewport={{ once: true }}
                whileHover={{ y: -3 }}
              >
                <h4 className="text-md font-semibold text-white">{member.name}</h4>
                <p className="text-white/90 text-xs font-medium">{member.role}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default About
