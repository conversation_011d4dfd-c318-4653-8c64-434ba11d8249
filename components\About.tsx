'use client'

import { motion } from 'framer-motion'
import { Globe, Zap, Users, Clock } from 'lucide-react'

const About = () => {
  const features = [
    {
      icon: Globe,
      title: 'Global Presence',
      description: 'Operating across multiple continents with strategic investments in key markets worldwide.',
      gradient: 'from-teal to-teal-dark'
    },
    {
      icon: Zap,
      title: 'Innovative Approach',
      description: 'Pioneering investment strategies that capitalize on emerging opportunities and market trends.',
      gradient: 'from-orange to-orange-dark'
    },
    {
      icon: Users,
      title: 'Strategic Partnerships',
      description: 'Collaborating with industry leaders to create synergies and maximize investment returns.',
      gradient: 'from-gold to-yellow-600'
    },
    {
      icon: Clock,
      title: 'Proven Track Record',
      description: 'Over 15 years of consistent performance and value creation across diverse market conditions.',
      gradient: 'from-teal-light to-teal'
    }
  ]

  return (
    <section id="about" className="py-20 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-white via-slate-50 to-teal-50">
        <div className="absolute top-0 left-0 w-full h-full opacity-5">
          <div className="absolute top-20 left-20 w-64 h-64 bg-teal rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-20 w-96 h-96 bg-orange rounded-full blur-3xl"></div>
        </div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-dark mb-4">
            Our Story
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-teal to-orange mx-auto mb-6"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Founded on excellence and driven by innovation, Kaiteur International Holdings
            has evolved into a global powerhouse in strategic investments and business development.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Story Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <div className="glass p-8 rounded-2xl hover-lift">
              <h3 className="text-2xl font-semibold text-teal mb-4">Founded on Excellence</h3>
              <p className="text-gray-600 mb-4 leading-relaxed">
                Kaiteur International Holdings was established with a vision to create a global
                investment platform that delivers exceptional value to our partners and stakeholders.
              </p>
              <p className="text-gray-600 mb-4 leading-relaxed">
                Starting as a focused investment firm, we&apos;ve grown into a diversified holding
                company with interests across multiple industries and markets worldwide.
              </p>
              <p className="text-gray-600 leading-relaxed">
                Our journey has been defined by strategic acquisitions, innovative partnerships,
                and a commitment to sustainable growth that benefits all our stakeholders.
              </p>
            </div>
          </motion.div>

          {/* Features Grid */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="grid grid-cols-1 sm:grid-cols-2 gap-6"
          >
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                viewport={{ once: true }}
                className="glass p-6 rounded-xl hover-lift group"
                whileHover={{ scale: 1.02 }}
              >
                <div className={`w-12 h-12 rounded-lg bg-gradient-to-br ${feature.gradient} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-gray-dark mb-2">{feature.title}</h4>
                <p className="text-gray-600 text-sm leading-relaxed">{feature.description}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Values Section */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <h3 className="text-3xl font-bold text-gray-dark mb-8">Our Core Values</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              { title: 'Integrity', description: 'Conducting business with the highest ethical standards' },
              { title: 'Innovation', description: 'Embracing change and seeking new value creation opportunities' },
              { title: 'Collaboration', description: 'Building partnerships that drive mutual success' },
              { title: 'Excellence', description: 'Striving for the highest standards in everything we do' }
            ].map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                viewport={{ once: true }}
                className="bg-gradient-to-r from-teal to-orange p-6 rounded-xl hover-lift shadow-xl"
                whileHover={{ y: -5 }}
              >
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-white/20 backdrop-blur-sm border-2 border-white/30 flex items-center justify-center">
                  <span className="text-white font-bold text-xl">{value.title[0]}</span>
                </div>
                <h4 className="text-xl font-semibold text-white mb-2">{value.title}</h4>
                <p className="text-white/90 text-sm">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Leadership Team Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16 mt-20"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">Our Leadership Team</h2>
          <div className="w-20 h-1 bg-gradient-to-r from-orange-500 to-teal-600 mx-auto mb-6"></div>
          <p className="text-lg text-gray-700 max-w-2xl mx-auto">
            The visionary leaders guiding our humanitarian mission forward.
          </p>
        </motion.div>

        {/* Founders Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {/* Ien Chapman */}
          <motion.div
            className="bg-gradient-to-r from-teal to-orange p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            whileHover={{ y: -5 }}
          >
            <h3 className="text-2xl font-bold text-white mb-2">Ien Chapman</h3>
            <p className="text-white/90 font-medium text-lg mb-4">Founder & CEO</p>
            <p className="text-white/80">Veteran | Entrepreneur | Visionary</p>
            <p className="text-white/80 mt-4 text-sm">
              A decorated veteran with a passion for serving others, Ien brings strategic leadership and a commitment to excellence in all our humanitarian initiatives.
            </p>
          </motion.div>

          {/* Arlene Chapman */}
          <motion.div
            className="bg-gradient-to-r from-teal to-orange p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            whileHover={{ y: -5 }}
          >
            <h3 className="text-2xl font-bold text-white mb-2">Arlene Chapman</h3>
            <p className="text-white/90 font-medium text-lg mb-4">Co-Founder</p>
            <p className="text-white/80">Community Leader | Strategist | Philanthropist</p>
            <p className="text-white/80 mt-4 text-sm">
              With a heart for service and a mind for business, Arlene ensures our operations align with our mission to create lasting, positive change in communities worldwide.
            </p>
          </motion.div>
        </div>

        {/* C-Team Section */}
        <motion.div
          className="mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.1 }}
          viewport={{ once: true }}
        >
          <h3 className="text-2xl font-bold text-gray-800 mb-8 text-center">Executive Leadership</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              { name: 'Wayne Robinson', role: 'Chief Operating Officer' },
              { name: 'Xavier Grier', role: 'Chief Marketing Officer' },
              { name: 'Johnny Walker', role: 'Chief Human Resources Officer' },
              { name: 'Joel Rodriguez', role: 'Chief Information Officer' }
            ].map((member, index) => (
              <motion.div
                key={member.name}
                className="bg-gradient-to-r from-teal to-orange p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 text-center"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 * index }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
              >
                <h4 className="text-lg font-semibold text-white mb-1">{member.name}</h4>
                <p className="text-white/90 text-sm font-medium">{member.role}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Directors Section */}
        <motion.div
          className="mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <h3 className="text-2xl font-bold text-gray-800 mb-8 text-center">Directors</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-3xl mx-auto">
            {[
              { name: 'Mary Young', role: 'Sr. Executive Director' },
              { name: 'Alfred Strachan', role: 'Jr. Executive Director' }
            ].map((member, index) => (
              <motion.div
                key={member.name}
                className="bg-gradient-to-r from-teal to-orange p-4 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 text-center"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 * index }}
                viewport={{ once: true }}
                whileHover={{ y: -3 }}
              >
                <h4 className="text-md font-semibold text-white">{member.name}</h4>
                <p className="text-white/90 text-xs font-medium">{member.role}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default About
