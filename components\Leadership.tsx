'use client'

import { motion } from 'framer-motion'
import { User } from 'lucide-react'

const Leadership = () => {
  const leaders = [
    {
      name: '<PERSON>',
      position: 'Chief Executive Officer',
      bio: 'With over 25 years of experience in global finance and investment, <PERSON> leads our strategic vision and international expansion.',
      experience: '25+ years',
      specialization: 'Global Finance & Strategy',
      education: 'MBA Harvard Business School',
      gradient: 'from-teal to-teal-dark'
    },
    {
      name: '<PERSON>',
      position: 'Chief Financial Officer',
      bio: '<PERSON> brings extensive expertise in financial strategy and international markets, overseeing our global investment portfolio.',
      experience: '20+ years',
      specialization: 'Financial Strategy & Markets',
      education: 'CFA, MSc London School of Economics',
      gradient: 'from-orange to-orange-dark'
    },
    {
      name: '<PERSON>',
      position: 'Chief Operations Officer',
      bio: '<PERSON> leads our operational excellence initiatives, ensuring efficient execution of our strategic objectives across all markets.',
      experience: '18+ years',
      specialization: 'Operations & Process Excellence',
      education: 'MBA Wharton School',
      gradient: 'from-gold to-yellow-600'
    },
    {
      name: 'Dr. <PERSON>',
      position: 'Chief Technology Officer',
      bio: 'Elena drives our digital transformation and technology innovation initiatives across all portfolio companies.',
      experience: '15+ years',
      specialization: 'Technology & Innovation',
      education: 'PhD Computer Science MIT',
      gradient: 'from-purple-500 to-purple-700'
    },
    {
      name: '<PERSON>',
      position: 'Chief Investment Officer',
      bio: '<PERSON> oversees our investment strategy and portfolio management, with deep expertise in emerging markets.',
      experience: '22+ years',
      specialization: 'Investment Strategy',
      education: 'MBA Stanford Graduate School',
      gradient: 'from-indigo-500 to-indigo-700'
    },
    {
      name: 'Amanda Foster',
      position: 'Chief Legal Officer',
      bio: 'Amanda leads our legal and compliance functions, ensuring regulatory adherence across all global operations.',
      experience: '16+ years',
      specialization: 'Corporate Law & Compliance',
      education: 'JD Yale Law School',
      gradient: 'from-teal-light to-teal'
    }
  ]

  return (
    <section id="leadership" className="py-20 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-gray-900 to-teal-900">
        <div className="absolute inset-0 bg-hero-pattern opacity-5"></div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-20 w-64 h-64 bg-orange/10 rounded-full blur-3xl floating"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-teal/10 rounded-full blur-3xl floating" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Our Leadership
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-teal to-orange mx-auto mb-6"></div>
          <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed">
            Meet the experienced team guiding our strategic vision and global operations,
            bringing together decades of expertise across diverse industries and markets.
          </p>
        </motion.div>

        {/* Leadership Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {leaders.map((leader, index) => (
            <motion.div
              key={leader.name}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.6 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-gradient-to-r from-teal to-orange rounded-2xl overflow-hidden hover-lift p-0.5">
                <div className="glass-dark rounded-xl h-full">
                {/* Profile Image Placeholder */}
                <div className="h-64 bg-gradient-to-r from-teal to-orange flex items-center justify-center relative overflow-hidden">
                  <div className="absolute inset-0 bg-black/20"></div>
                  <User className="w-24 h-24 text-white/80 relative z-10" />

                  {/* Overlay on hover */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>


                </div>

                {/* Content */}
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-teal to-orange mb-1">{leader.name}</h3>
                  <p className="text-teal font-medium mb-4">{leader.position}</p>
                  <p className="text-white/70 text-sm mb-4 leading-relaxed">{leader.bio}</p>

                  {/* Details */}
                  <div className="space-y-2 text-xs">
                    <div className="flex justify-between">
                      <span className="text-white/60">Experience:</span>
                      <span className="text-white/80 font-medium">{leader.experience}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/60">Specialization:</span>
                      <span className="text-white/80 font-medium text-right">{leader.specialization}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/60">Education:</span>
                      <span className="text-white/80 font-medium text-right">{leader.education}</span>
                    </div>
                  </div>
                </div>

                </div>
                {/* Hover Effect */}
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-teal to-orange opacity-0 group-hover:opacity-20 transition-opacity duration-300 pointer-events-none"></div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="glass-dark p-8 rounded-2xl max-w-4xl mx-auto">
            <h3 className="text-3xl font-bold text-white mb-4">
              Join Our Leadership Journey
            </h3>
            <p className="text-white/80 mb-8 text-lg">
              We&apos;re always looking for exceptional talent to join our leadership team.
              If you share our vision and values, we&apos;d love to hear from you.
            </p>
            <motion.button
              className="px-8 py-4 bg-gradient-to-r from-teal to-orange text-white font-semibold rounded-full shadow-xl hover:shadow-2xl transition-all duration-300"
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => {
                const element = document.querySelector('#contact')
                if (element) {
                  element.scrollIntoView({ behavior: 'smooth' })
                }
              }}
            >
              Explore Opportunities
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Leadership
