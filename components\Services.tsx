'use client'

import { motion } from 'framer-motion'
import { TrendingUp, Building, Users, Target, Globe, Briefcase } from 'lucide-react'

const Services = () => {
  const services = [
    {
      icon: TrendingUp,
      title: 'Investment Management',
      description: 'Comprehensive portfolio management with focus on long-term value creation and risk optimization.',
      features: ['Portfolio Optimization', 'Risk Assessment', 'Market Analysis', 'Performance Tracking'],
      gradient: 'from-teal to-teal-dark'
    },
    {
      icon: Building,
      title: 'Business Development',
      description: 'Strategic guidance for business expansion, market entry, and operational excellence.',
      features: ['Market Entry Strategy', 'Operational Excellence', 'Growth Planning', 'Process Optimization'],
      gradient: 'from-orange to-orange-dark'
    },
    {
      icon: Users,
      title: 'Strategic Consulting',
      description: 'Expert advisory services for complex business challenges and strategic decision-making.',
      features: ['Strategic Planning', 'Market Research', 'Competitive Analysis', 'Business Modeling'],
      gradient: 'from-gold to-yellow-600'
    },
    {
      icon: Target,
      title: 'Mergers & Acquisitions',
      description: 'End-to-end M&A services from target identification to post-merger integration.',
      features: ['Due Diligence', 'Valuation Services', 'Deal Structuring', 'Integration Support'],
      gradient: 'from-teal-light to-teal'
    },
    {
      icon: Globe,
      title: 'Global Market Entry',
      description: 'Facilitating international expansion with local market expertise and regulatory guidance.',
      features: ['Market Assessment', 'Regulatory Compliance', 'Local Partnerships', 'Cultural Integration'],
      gradient: 'from-purple-500 to-purple-700'
    },
    {
      icon: Briefcase,
      title: 'Corporate Finance',
      description: 'Comprehensive financial solutions including capital raising and financial restructuring.',
      features: ['Capital Raising', 'Financial Restructuring', 'Debt Management', 'Financial Planning'],
      gradient: 'from-indigo-500 to-indigo-700'
    }
  ]

  return (
    <section id="services" className="py-20 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-gray-900 to-teal-900">
        <div className="absolute inset-0 bg-hero-pattern opacity-5"></div>

        {/* Floating Elements */}
        <div className="absolute top-20 right-20 w-64 h-64 bg-orange/10 rounded-full blur-3xl floating"></div>
        <div className="absolute bottom-20 left-20 w-96 h-96 bg-teal/10 rounded-full blur-3xl floating" style={{ animationDelay: '3s' }}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Our Services
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-teal to-orange mx-auto mb-6"></div>
          <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed">
            Comprehensive solutions designed to drive growth, optimize performance,
            and create sustainable value across all aspects of your business.
          </p>
        </motion.div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <motion.div
              key={service.title}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.6 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="glass-dark p-8 rounded-2xl hover-lift h-full">
                {/* Icon */}
                <div className={`w-16 h-16 rounded-xl bg-gradient-to-br ${service.gradient} flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <service.icon className="w-8 h-8 text-white" />
                </div>

                {/* Content */}
                <h3 className="text-2xl font-semibold text-white mb-4">{service.title}</h3>
                <p className="text-white/70 mb-6 leading-relaxed">{service.description}</p>

                {/* Features */}
                <ul className="space-y-2">
                  {service.features.map((feature, featureIndex) => (
                    <motion.li
                      key={feature}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ delay: (index * 0.1) + (featureIndex * 0.05), duration: 0.4 }}
                      viewport={{ once: true }}
                      className="flex items-center text-white/60 text-sm"
                    >
                      <div className="w-1.5 h-1.5 bg-gradient-to-r from-teal to-orange rounded-full mr-3"></div>
                      {feature}
                    </motion.li>
                  ))}
                </ul>

                {/* Hover Effect */}
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-teal/5 to-orange/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="glass-dark p-8 rounded-2xl max-w-4xl mx-auto">
            <h3 className="text-3xl font-bold text-white mb-4">
              Ready to Transform Your Business?
            </h3>
            <p className="text-white/80 mb-8 text-lg">
              Let&apos;s discuss how our expertise can help you achieve your strategic objectives
              and unlock new opportunities for growth.
            </p>
            <motion.button
              className="px-8 py-4 bg-gradient-to-r from-teal to-orange text-white font-semibold rounded-full shadow-xl hover:shadow-2xl transition-all duration-300"
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => {
                const element = document.querySelector('#contact')
                if (element) {
                  element.scrollIntoView({ behavior: 'smooth' })
                }
              }}
            >
              Start the Conversation
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Services
