# Kaiteur International Holdings

A modern, visually striking website for Kaiteur International Holdings - a global leader in strategic investments and business development.

## 🚀 Features

- **Modern Design**: Glass morphism effects, gradient backgrounds, and smooth animations
- **Responsive**: Mobile-first design that works on all devices
- **Performance Optimized**: Built with Next.js 14 for optimal performance
- **Interactive**: Smooth scrolling, hover effects, and engaging animations
- **Professional**: Clean, corporate design suitable for a holding company

## 🛠️ Tech Stack

- **Framework**: Next.js 14 with App Router
- **Styling**: Tailwind CSS with custom gradients and animations
- **Animations**: Framer Motion for smooth, professional animations
- **Icons**: Lucide React for modern, consistent icons
- **Typography**: Montserrat font for professional appearance
- **Deployment**: Optimized for Heroku deployment

## 📦 Installation

1. Clone the repository:
```bash
git clone <your-repo-url>
cd kaiteur-holdings
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🚀 Deployment to Heroku

### Prerequisites
- Heroku CLI installed
- Git repository initialized

### Steps

1. **Login to Heroku**:
```bash
heroku login
```

2. **Create a new Heroku app**:
```bash
heroku create your-app-name
```

3. **Set Node.js buildpack**:
```bash
heroku buildpacks:set heroku/nodejs
```

4. **Deploy**:
```bash
git add .
git commit -m "Initial deployment"
git push heroku main
```

### Alternative: Deploy Button
You can also deploy directly using the Heroku Deploy Button by clicking:

[![Deploy](https://www.herokucdn.com/deploy/button.svg)](https://heroku.com/deploy)

## 🎨 Design Features

### Modern Visual Elements
- **Glass Morphism**: Transparent boxes with backdrop blur effects
- **Gradient Backgrounds**: Beautiful color transitions using teal, orange, and gold
- **Floating Animations**: Subtle floating elements for visual interest
- **Hover Effects**: Interactive elements that respond to user interaction

### Color Scheme
- **Primary**: Teal (#0d9488) - Professional and trustworthy
- **Secondary**: Orange (#f97316) - Energy and innovation
- **Accent**: Gold (#d4af37) - Premium and excellence
- **Supporting**: Various shades of gray for text and backgrounds

### Typography
- **Font**: Montserrat - Modern, professional, and highly readable
- **Weights**: 300-900 for various text hierarchies
- **Spacing**: Optimized line heights and letter spacing

## 📱 Sections

1. **Hero**: Eye-catching introduction with animated elements
2. **About**: Company story and core values
3. **Services**: Comprehensive service offerings
4. **Portfolio**: Global investment presence
5. **Leadership**: Executive team profiles
6. **Contact**: Contact form and office information
7. **Footer**: Links, newsletter signup, and company info

## 🔧 Customization

### Colors
Edit `tailwind.config.js` to modify the color scheme:

```javascript
colors: {
  teal: {
    light: '#4fd1c5',
    DEFAULT: '#0d9488',
    dark: '#0f766e',
  },
  // Add your custom colors
}
```

### Content
Update the content in each component file:
- `components/Hero.tsx` - Hero section content
- `components/About.tsx` - Company information
- `components/Services.tsx` - Service offerings
- `components/Portfolio.tsx` - Investment data
- `components/Leadership.tsx` - Team information
- `components/Contact.tsx` - Contact details

### Animations
Modify animations in component files using Framer Motion:

```javascript
<motion.div
  initial={{ opacity: 0, y: 30 }}
  animate={{ opacity: 1, y: 0 }}
  transition={{ duration: 0.8 }}
>
  Your content
</motion.div>
```

## 📊 Performance

- **Lighthouse Score**: Optimized for 90+ scores
- **Core Web Vitals**: Excellent LCP, FID, and CLS scores
- **Image Optimization**: Next.js automatic image optimization
- **Code Splitting**: Automatic code splitting for faster loads

## 🔒 Security

- **HTTPS**: Enforced in production
- **Headers**: Security headers configured
- **Dependencies**: Regular security updates

## 📞 Support

For support or questions about this project, please contact:
- Email: <EMAIL>
- Website: [Your Website URL]

## 📄 License

This project is proprietary and confidential. All rights reserved by Kaiteur International Holdings.

---

Built with ❤️ using Next.js, Tailwind CSS, and Framer Motion.
