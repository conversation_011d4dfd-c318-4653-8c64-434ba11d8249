'use client'

import { motion } from 'framer-motion'
import { Building2, Search, FileCheck, Handshake, TrendingUp, Shield } from 'lucide-react'
import Navigation from '@/components/Navigation'
import Footer from '@/components/Footer'
import Link from 'next/link'

export default function MergersAcquisitions() {
  const features = [
    {
      icon: Search,
      title: 'Due Diligence',
      description: 'Comprehensive due diligence services to assess financial, legal, and operational aspects of potential transactions.'
    },
    {
      icon: TrendingUp,
      title: 'Valuation Services',
      description: 'Expert business valuation using multiple methodologies to ensure fair and accurate transaction pricing.'
    },
    {
      icon: FileCheck,
      title: 'Deal Structuring',
      description: 'Strategic deal structuring to optimize tax efficiency, risk allocation, and value creation for all parties.'
    },
    {
      icon: Handshake,
      title: 'Integration Support',
      description: 'Post-merger integration planning and execution to ensure seamless transitions and value realization.'
    }
  ]

  const services = [
    'Buy-Side Advisory Services',
    'Sell-Side Transaction Management',
    'Strategic Partnership Development',
    'Joint Venture Structuring',
    'Divestiture Planning',
    'Cross-Border Transaction Expertise'
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-white to-gray-50">
      <Navigation />
      {/* Header */}
      <div className="bg-gradient-to-r from-teal-600 to-orange-500 text-white py-20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <Building2 className="w-16 h-16 text-white/90 mx-auto mb-6" />
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Mergers & Acquisitions</h1>
            <p className="text-xl text-white/90 max-w-3xl mx-auto">
              Strategic M&A advisory services to help businesses grow through acquisitions, mergers, and strategic partnerships that create lasting value.
            </p>
          </motion.div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Overview */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.8 }}
          className="mb-16"
        >
          <h2 className="text-3xl font-bold text-gray-900 mb-6 text-center">Strategic Growth Through M&A</h2>
          <p className="text-lg text-teal-600 leading-relaxed max-w-4xl mx-auto text-center">
            Our M&A team brings decades of experience in structuring and executing complex transactions across diverse industries. We provide end-to-end advisory services that maximize value while minimizing risk throughout the transaction lifecycle.
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 + index * 0.1, duration: 0.6 }}
              className="bg-white rounded-xl shadow-sm border border-gray-100 p-8 hover:shadow-lg transition-shadow duration-300"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-teal-50 rounded-lg flex items-center justify-center mr-4">
                  <feature.icon className="w-6 h-6 text-teal-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900">{feature.title}</h3>
              </div>
              <p className="text-teal-600 leading-relaxed">{feature.description}</p>
            </motion.div>
          ))}
        </div>

        {/* Services Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7, duration: 0.8 }}
          className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-16"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">Our M&A Services</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {services.map((service, index) => (
              <div key={index} className="flex items-start">
                <div className="w-2 h-2 bg-indigo-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <span className="text-teal-600">{service}</span>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Transaction Process */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.8 }}
          className="mb-16"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">Our M&A Process</h3>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            {[
              { step: '01', title: 'Strategy', description: 'Define M&A objectives and target criteria' },
              { step: '02', title: 'Sourcing', description: 'Identify and evaluate potential targets' },
              { step: '03', title: 'Due Diligence', description: 'Comprehensive analysis and risk assessment' },
              { step: '04', title: 'Negotiation', description: 'Structure and negotiate optimal terms' },
              { step: '05', title: 'Integration', description: 'Execute post-merger integration plan' }
            ].map((item, index) => (
              <div key={index} className="text-center">
                <div className="w-12 h-12 bg-gradient-to-r from-teal-600 to-orange-500 text-white rounded-full flex items-center justify-center mx-auto mb-3 text-sm font-bold">
                  {item.step}
                </div>
                <h4 className="text-sm font-semibold text-gray-900 mb-1">{item.title}</h4>
                <p className="text-teal-600 text-xs">{item.description}</p>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Value Creation */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.85, duration: 0.8 }}
          className="bg-gray-50 rounded-2xl p-8 mb-16"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">Value Creation Focus</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              { 
                title: 'Strategic Synergies', 
                description: 'Identifying and capturing revenue synergies, cost savings, and operational efficiencies.',
                icon: TrendingUp
              },
              { 
                title: 'Risk Mitigation', 
                description: 'Comprehensive risk assessment and mitigation strategies throughout the transaction.',
                icon: Shield
              },
              { 
                title: 'Cultural Integration', 
                description: 'Ensuring successful cultural integration and talent retention post-transaction.',
                icon: Handshake
              }
            ].map((item, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-teal-600 to-orange-500 text-white rounded-full flex items-center justify-center mx-auto mb-4">
                  <item.icon className="w-8 h-8" />
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">{item.title}</h4>
                <p className="text-teal-600">{item.description}</p>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Track Record */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9, duration: 0.8 }}
          className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-16"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">Our Track Record</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              { metric: '$2B+', label: 'Transaction Value', description: 'Total value of completed M&A transactions' },
              { metric: '20+', label: 'Deals Completed', description: 'Successful transactions across various industries' },
              { metric: '95%', label: 'Success Rate', description: 'Percentage of transactions successfully closed' }
            ].map((item, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl font-bold text-teal-600 mb-2">{item.metric}</div>
                <div className="text-lg font-semibold text-gray-900 mb-2">{item.label}</div>
                <p className="text-teal-600 text-sm">{item.description}</p>
              </div>
            ))}
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1, duration: 0.8 }}
          className="bg-gradient-to-r from-teal-600 to-orange-500 rounded-2xl p-8 text-center text-white"
        >
          <h3 className="text-2xl font-bold mb-4">Ready to Explore M&A Opportunities?</h3>
          <p className="text-lg mb-6 text-white/90">
            Let our M&A experts help you identify and execute strategic transactions that drive growth.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/#contact"
              className="px-8 py-3 bg-white text-teal-600 font-semibold rounded-lg hover:bg-teal-50 transition-colors duration-300"
            >
              Discuss Your M&amp;A Strategy
            </Link>
            <Link
              href="/"
              className="px-8 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-teal-600 transition-all duration-300"
            >
              Learn More
            </Link>
          </div>
        </motion.div>
      </div>
      <Footer />
    </div>
  )
}
