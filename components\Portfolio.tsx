'use client'

import { motion } from 'framer-motion'
import { MapPin, TrendingUp, Users, DollarSign } from 'lucide-react'

const Portfolio = () => {
  const regions = [
    {
      name: 'North America',
      description: 'Strategic investments in technology, real estate, and financial services sectors.',
      investments: '$850M',
      companies: '12',
      color: 'from-teal to-teal-dark',
      position: { top: '25%', left: '20%' }
    },
    {
      name: 'Europe',
      description: 'Focused on sustainable energy, infrastructure, and manufacturing ventures.',
      investments: '$620M',
      companies: '8',
      color: 'from-orange to-orange-dark',
      position: { top: '20%', left: '50%' }
    },
    {
      name: 'Asia Pacific',
      description: 'Expanding presence in emerging markets with high growth potential.',
      investments: '$740M',
      companies: '15',
      color: 'from-gold to-yellow-600',
      position: { top: '35%', left: '75%' }
    },
    {
      name: 'Middle East',
      description: 'Partnerships in energy, hospitality, and infrastructure development.',
      investments: '$420M',
      companies: '6',
      color: 'from-purple-500 to-purple-700',
      position: { top: '45%', left: '55%' }
    },
    {
      name: 'Africa',
      description: 'Investments in natural resources, agriculture, and telecommunications.',
      investments: '$380M',
      companies: '9',
      color: 'from-indigo-500 to-indigo-700',
      position: { top: '60%', left: '50%' }
    },
    {
      name: 'South America',
      description: 'Growing portfolio in renewable energy and agricultural technology.',
      investments: '$290M',
      companies: '5',
      color: 'from-teal-light to-teal',
      position: { top: '70%', left: '25%' }
    }
  ]

  const stats = [
    { icon: DollarSign, value: '$3.3B+', label: 'Total Investments' },
    { icon: TrendingUp, value: '55+', label: 'Portfolio Companies' },
    { icon: MapPin, value: '25+', label: 'Countries' },
    { icon: Users, value: '50K+', label: 'Jobs Created' }
  ]

  return (
    <section id="portfolio" className="py-20 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-white via-slate-50 to-teal-50">
        <div className="absolute top-0 left-0 w-full h-full opacity-5">
          <div className="absolute top-40 right-40 w-96 h-96 bg-orange rounded-full blur-3xl"></div>
          <div className="absolute bottom-40 left-40 w-64 h-64 bg-teal rounded-full blur-3xl"></div>
        </div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-dark mb-4">
            Global Portfolio
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-teal to-orange mx-auto mb-6"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Our strategic investments span across continents, creating value in diverse 
            markets and driving innovation across multiple industries.
          </p>
        </motion.div>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16"
        >
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.1, duration: 0.6 }}
              viewport={{ once: true }}
              className="glass p-6 rounded-xl text-center hover-lift"
              whileHover={{ y: -5 }}
            >
              <stat.icon className="w-8 h-8 mx-auto mb-3 text-teal" />
              <div className="text-2xl md:text-3xl font-bold text-gray-dark mb-1">{stat.value}</div>
              <div className="text-sm text-gray-600">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>

        {/* World Map Visualization */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="relative mb-16"
        >
          <div className="glass p-8 rounded-2xl">
            <h3 className="text-2xl font-bold text-gray-dark mb-8 text-center">Investment Presence</h3>
            
            {/* Simplified World Map Container */}
            <div className="relative h-96 bg-gradient-to-br from-slate-100 to-slate-200 rounded-xl overflow-hidden">
              {/* World Map SVG Background */}
              <svg className="w-full h-full opacity-20" viewBox="0 0 1000 500" xmlns="http://www.w3.org/2000/svg">
                {/* Simplified continents */}
                <path d="M100,200 Q200,150 300,200 Q400,180 500,200 Q600,190 700,200 Q800,180 900,200" 
                      fill="none" stroke="#0d9488" strokeWidth="30" strokeLinecap="round" />
                <path d="M150,120 Q250,80 350,120 Q450,100 550,120 Q650,110 750,120" 
                      fill="none" stroke="#0d9488" strokeWidth="20" strokeLinecap="round" />
                <path d="M200,280 Q300,240 400,280 Q500,260 600,280 Q700,270 800,280" 
                      fill="none" stroke="#0d9488" strokeWidth="25" strokeLinecap="round" />
              </svg>

              {/* Investment Markers */}
              {regions.map((region, index) => (
                <motion.div
                  key={region.name}
                  initial={{ opacity: 0, scale: 0 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.2, duration: 0.6 }}
                  viewport={{ once: true }}
                  className="absolute transform -translate-x-1/2 -translate-y-1/2 group cursor-pointer"
                  style={{ top: region.position.top, left: region.position.left }}
                  whileHover={{ scale: 1.1 }}
                >
                  <div className={`w-4 h-4 rounded-full bg-gradient-to-br ${region.color} shadow-lg`}>
                    <div className="absolute inset-0 rounded-full bg-gradient-to-br from-white/30 to-transparent"></div>
                  </div>
                  
                  {/* Tooltip */}
                  <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
                    <div className="glass p-3 rounded-lg shadow-xl min-w-48">
                      <h4 className="font-semibold text-gray-dark text-sm">{region.name}</h4>
                      <p className="text-xs text-gray-600 mt-1">{region.description}</p>
                      <div className="flex justify-between mt-2 text-xs">
                        <span className="text-teal font-medium">{region.investments}</span>
                        <span className="text-orange font-medium">{region.companies} companies</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Regional Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {regions.map((region, index) => (
            <motion.div
              key={region.name}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.6 }}
              viewport={{ once: true }}
              className="glass p-6 rounded-xl hover-lift"
              whileHover={{ y: -5 }}
            >
              <div className={`w-12 h-12 rounded-lg bg-gradient-to-br ${region.color} flex items-center justify-center mb-4`}>
                <MapPin className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-gray-dark mb-2">{region.name}</h3>
              <p className="text-gray-600 text-sm mb-4 leading-relaxed">{region.description}</p>
              <div className="flex justify-between items-center">
                <div>
                  <div className="text-lg font-bold text-teal">{region.investments}</div>
                  <div className="text-xs text-gray-500">Invested</div>
                </div>
                <div>
                  <div className="text-lg font-bold text-orange">{region.companies}</div>
                  <div className="text-xs text-gray-500">Companies</div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Portfolio
