@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 15, 23, 42; /* slate-900 */
  --background-start-rgb: 255, 255, 255;
  --background-end-rgb: 255, 255, 255;
  --primary-teal: #0d9488; /* teal-600 */
  --primary-orange: #ea580c; /* orange-600 */
  --gradient-teal: rgba(13, 148, 136, 0.15);
  --gradient-orange: rgba(234, 88, 12, 0.05);
  --gradient-teal-strong: rgba(13, 148, 136, 0.2);
  --gradient-orange-strong: rgba(234, 88, 12, 0.08);
  --card-bg: rgba(255, 255, 255, 0.95);
  --footer-bg: linear-gradient(135deg, rgba(167, 243, 208, 0.4) 0%, rgba(254, 215, 170, 0.3) 100%); /* Lighter teal-orange gradient */
  --footer-text: #334155; /* slate-700 */
  --footer-border: #e2e8f0; /* slate-200 */
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Montserrat', sans-serif;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  color: rgb(var(--foreground-rgb));
  background: 
    linear-gradient(
      145deg,
      rgba(var(--gradient-green), 0.08) 0%,
      rgba(var(--background-color), 1) 30%,
      rgba(var(--background-color), 1) 70%,
      rgba(var(--gradient-orange), 0.08) 100%
    ),
    rgb(var(--background-color));
  min-height: 100vh;
}

/* Ensure all main content sections have white background */
section, main, .section {
  background-color: white;
  position: relative;
  z-index: 1;
}

/* Add gradient accents to section borders */
section::before, .section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, rgba(var(--gradient-green), 0.8), rgba(var(--gradient-orange), 0.8));
  z-index: 2;
}

a {
  color: inherit;
  text-decoration: none;
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid #0d9488;
  outline-offset: 2px;
}

button:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid #0d9488;
  outline-offset: 2px;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus\:not-sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #0d9488;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #0f766e;
}

/* Glass morphism utility classes */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #0d9488 0%, #f97316 50%, #d4af37 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Custom animations */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Parallax container */
.parallax-container {
  height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
  perspective: 1px;
}

.parallax-element {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.parallax-back {
  transform: translateZ(-1px) scale(2);
}

.parallax-base {
  transform: translateZ(0);
}

/* Floating elements */
.floating {
  animation: float 6s ease-in-out infinite;
}

.floating:nth-child(2) {
  animation-delay: 2s;
}

.floating:nth-child(3) {
  animation-delay: 4s;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Loading animation */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: dots 1.5s steps(5, end) infinite;
}

@keyframes dots {
  0%, 20% {
    color: rgba(0,0,0,0);
    text-shadow:
      .25em 0 0 rgba(0,0,0,0),
      .5em 0 0 rgba(0,0,0,0);
  }
  40% {
    color: #0d9488;
    text-shadow:
      .25em 0 0 rgba(0,0,0,0),
      .5em 0 0 rgba(0,0,0,0);
  }
  60% {
    text-shadow:
      .25em 0 0 #0d9488,
      .5em 0 0 rgba(0,0,0,0);
  }
  80%, 100% {
    text-shadow:
      .25em 0 0 #0d9488,
      .5em 0 0 #0d9488;
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .text-4xl {
    font-size: 2rem !important;
    line-height: 2.5rem !important;
  }

  .text-5xl {
    font-size: 2.5rem !important;
    line-height: 3rem !important;
  }

  .text-6xl {
    font-size: 3rem !important;
    line-height: 3.5rem !important;
  }

  /* Ensure proper spacing on mobile */
  .py-20 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }

  .py-16 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important;
  }

  /* Better touch targets */
  button, a {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Extra small devices */
@media (max-width: 480px) {
  .text-xl {
    font-size: 1.125rem !important;
    line-height: 1.75rem !important;
  }

  .text-2xl {
    font-size: 1.5rem !important;
    line-height: 2rem !important;
  }

  .text-3xl {
    font-size: 1.875rem !important;
    line-height: 2.25rem !important;
  }

  .px-8 {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  .gap-8 {
    gap: 1rem !important;
  }
}

/* Landscape mobile optimization */
@media (max-height: 500px) and (orientation: landscape) {
  .py-20 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }

  .py-16 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
}
