
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Us - Kaiteur International Holdings</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        teal: {
                            light: '#4fd1c5',
                            DEFAULT: '#0d9488',
                            dark: '#0f766e',
                        },
                        gray: {
                            light: '#f3f4f6',
                            DEFAULT: '#6b7280',
                            dark: '#374151',
                        },
                        orange: {
                            light: '#fdba74',
                            DEFAULT: '#f97316',
                            dark: '#ea580c',
                        },
                        silver: '#c0c0c0',
                        gold: '#d4af37',
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Montserrat', sans-serif;
        }
        
        .hero-pattern {
            background-color: #0d9488;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        
        .animate-float {
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }
        
        .section-divider {
            position: relative;
            height: 50px;
            overflow: hidden;
        }
        
        .section-divider::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #fff;
            clip-path: polygon(0 0, 100% 100%, 100% 0);
        }
        
        .gold-gradient {
            background: linear-gradient(135deg, #d4af37 0%, #f2e279 50%, #d4af37 100%);
        }
        
        .silver-gradient {
            background: linear-gradient(135deg, #c0c0c0 0%, #e8e8e8 50%, #c0c0c0 100%);
        }
        
        .nav-link {
            position: relative;
        }
        
        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -2px;
            left: 0;
            background-color: #0d9488;
            transition: width 0.3s ease;
        }
        
        .nav-link:hover::after {
            width: 100%;
        }
        
        .value-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-md fixed w-full z-10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <div class="h-10 w-10 rounded-full bg-teal flex items-center justify-center">
                            <span class="text-white font-bold text-xl">K</span>
                        </div>
                        <span class="ml-2 text-gray-dark font-semibold text-lg">Kaiteur International Holdings</span>
                    </div>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#" class="nav-link text-gray-dark hover:text-teal font-medium">Home</a>
                    <a href="#" class="nav-link text-teal font-medium border-b-2 border-teal">About Us</a>
                    <a href="#" class="nav-link text-gray-dark hover:text-teal font-medium">Services</a>
                    <a href="#" class="nav-link text-gray-dark hover:text-teal font-medium">Portfolio</a>
                    <a href="#" class="nav-link text-gray-dark hover:text-teal font-medium">Contact</a>
                </div>
                <div class="flex md:hidden items-center">
                    <button id="mobile-menu-button" class="text-gray-dark hover:text-teal">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile menu -->
        <div id="mobile-menu" class="hidden md:hidden bg-white shadow-md">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="#" class="block px-3 py-2 rounded-md text-gray-dark hover:bg-teal hover:text-white">Home</a>
                <a href="#" class="block px-3 py-2 rounded-md bg-teal text-white">About Us</a>
                <a href="#" class="block px-3 py-2 rounded-md text-gray-dark hover:bg-teal hover:text-white">Services</a>
                <a href="#" class="block px-3 py-2 rounded-md text-gray-dark hover:bg-teal hover:text-white">Portfolio</a>
                <a href="#" class="block px-3 py-2 rounded-md text-gray-dark hover:bg-teal hover:text-white">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-pattern pt-32 pb-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 mb-10 md:mb-0">
                    <h1 class="text-4xl md:text-5xl font-bold text-white leading-tight">About Kaiteur International Holdings</h1>
                    <p class="mt-4 text-lg text-white opacity-90">A global leader in investment and business development, creating sustainable value across diverse markets.</p>
                    <div class="mt-8 flex space-x-4">
                        <a href="#our-story" class="px-6 py-3 bg-white text-teal font-medium rounded-md shadow-md hover:shadow-lg transition duration-300">Our Story</a>
                        <a href="#contact" class="px-6 py-3 border border-white text-white font-medium rounded-md hover:bg-white hover:text-teal transition duration-300">Contact Us</a>
                    </div>
                </div>
                <div class="md:w-1/2 flex justify-center">
                    <div class="relative">
                        <div class="absolute -top-6 -left-6 w-64 h-64 bg-orange opacity-20 rounded-full animate-float" style="animation-delay: 0.5s"></div>
                        <div class="absolute -bottom-6 -right-6 w-64 h-64 bg-teal-light opacity-20 rounded-full animate-float" style="animation-delay: 1s"></div>
                        <svg class="w-80 h-80 relative z-10" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                            <path fill="#FFFFFF" d="M47.7,-57.2C59.9,-45.8,67.2,-29.6,69.4,-13.1C71.7,3.4,68.8,20.2,60.2,32.8C51.5,45.4,37,53.8,21.8,58.9C6.6,64,-9.3,65.8,-24.4,61.7C-39.5,57.6,-53.8,47.6,-62.3,33.5C-70.8,19.3,-73.5,1,-69.1,-14.8C-64.7,-30.6,-53.2,-43.9,-39.7,-54.9C-26.2,-65.9,-10.7,-74.6,3.4,-78.5C17.5,-82.4,35.5,-81.5,47.7,-72.2Z" transform="translate(100 100)" />
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="text-center">
                                <div class="text-teal text-5xl font-bold">15+</div>
                                <div class="text-gray-dark font-medium mt-2">Years of Excellence</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Our Story Section -->
    <section id="our-story" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-dark">Our Story</h2>
                <div class="mt-2 h-1 w-24 bg-teal mx-auto"></div>
            </div>
            
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 mb-10 md:mb-0">
                    <div class="relative">
                        <div class="absolute inset-0 bg-teal rounded-lg transform rotate-3"></div>
                        <div class="relative bg-white p-8 rounded-lg shadow-lg border border-gray-200">
                            <h3 class="text-2xl font-semibold text-teal mb-4">Founded on Excellence</h3>
                            <p class="text-gray-600 mb-4">Kaiteur International Holdings was established with a vision to create a global investment platform that delivers exceptional value to our partners and stakeholders.</p>
                            <p class="text-gray-600 mb-4">Starting as a small investment firm, we've grown into a diversified holding company with interests across multiple industries and markets worldwide.</p>
                            <p class="text-gray-600">Our journey has been defined by strategic acquisitions, innovative partnerships, and a commitment to sustainable growth that benefits all our stakeholders.</p>
                        </div>
                    </div>
                </div>
                <div class="md:w-1/2 md:pl-12">
                    <div class="space-y-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <div class="flex items-center justify-center h-12 w-12 rounded-md bg-teal text-white">
                                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h4 class="text-lg font-medium text-gray-dark">Global Presence</h4>
                                <p class="mt-1 text-gray-600">Operating across multiple continents with strategic investments in key markets worldwide.</p>
                            </div>
                        </div>
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <div class="flex items-center justify-center h-12 w-12 rounded-md bg-teal text-white">
                                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h4 class="text-lg font-medium text-gray-dark">Innovative Approach</h4>
                                <p class="mt-1 text-gray-600">Pioneering investment strategies that capitalize on emerging opportunities and market trends.</p>
                            </div>
                        </div>
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <div class="flex items-center justify-center h-12 w-12 rounded-md bg-teal text-white">
                                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h4 class="text-lg font-medium text-gray-dark">Strategic Partnerships</h4>
                                <p class="mt-1 text-gray-600">Collaborating with industry leaders to create synergies and maximize investment returns.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Values Section -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-dark">Our Core Values</h2>
                <div class="mt-2 h-1 w-24 bg-teal mx-auto"></div>
                <p class="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">The principles that guide our decisions and define our corporate culture.</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="value-card bg-white rounded-lg shadow-md p-6 border-t-4 border-teal transition duration-300">
                    <div class="h-12 w-12 rounded-full bg-teal bg-opacity-10 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-dark mb-2">Integrity</h3>
                    <p class="text-gray-600">We conduct our business with the highest ethical standards, ensuring transparency and accountability in all our dealings.</p>
                </div>
                
                <div class="value-card bg-white rounded-lg shadow-md p-6 border-t-4 border-orange transition duration-300">
                    <div class="h-12 w-12 rounded-full bg-orange bg-opacity-10 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-dark mb-2">Innovation</h3>
                    <p class="text-gray-600">We embrace change and continuously seek new ways to create value, staying ahead of market trends and technological advancements.</p>
                </div>
                
                <div class="value-card bg-white rounded-lg shadow-md p-6 border-t-4 border-silver transition duration-300">
                    <div class="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-dark mb-2">Collaboration</h3>
                    <p class="text-gray-600">We believe in the power of partnerships and teamwork, fostering relationships that drive mutual success and growth.</p>
                </div>
                
                <div class="value-card bg-white rounded-lg shadow-md p-6 border-t-4 gold-gradient transition duration-300">
                    <div class="h-12 w-12 rounded-full gold-gradient flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-dark mb-2">Excellence</h3>
                    <p class="text-gray-600">We strive for the highest standards in everything we do, continuously improving our processes and performance.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Leadership Section -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-dark">Our Leadership</h2>
                <div class="mt-2 h-1 w-24 bg-teal mx-auto"></div>
                <p class="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">Meet the experienced team guiding our strategic vision and global operations.</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-white rounded-lg shadow-md overflow-hidden transition duration-300 hover:shadow-xl">
                    <div class="h-64 bg-gray-200 flex items-center justify-center">
                        <svg class="w-32 h-32 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-dark">Jonathan Reynolds</h3>
                        <p class="text-teal font-medium">Chief Executive Officer</p>
                        <p class="mt-3 text-gray-600">With over 25 years of experience in global finance and investment, Jonathan leads our strategic vision and international expansion.</p>
                        <div class="mt-4 flex space-x-3">
                            <a href="#" class="text-gray hover:text-teal transition">
                                <i class="fab fa-linkedin text-lg"></i>
                            </a>
                            <a href="#" class="text-gray hover:text-teal transition">
                                <i class="fab fa-twitter text-lg"></i>
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-md overflow-hidden transition duration-300 hover:shadow-xl">
                    <div class="h-64 bg-gray-200 flex items-center justify-center">
                        <svg class="w-32 h-32 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-dark">Sarah Chen</h3>
                        <p class="text-teal font-medium">Chief Financial Officer</p>
                        <p class="mt-3 text-gray-600">Sarah brings extensive expertise in financial strategy and international markets, overseeing our global investment portfolio.</p>
                        <div class="mt-4 flex space-x-3">
                            <a href="#" class="text-gray hover:text-teal transition">
                                <i class="fab fa-linkedin text-lg"></i>
                            </a>
                            <a href="#" class="text-gray hover:text-teal transition">
                                <i class="fab fa-twitter text-lg"></i>
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-md overflow-hidden transition duration-300 hover:shadow-xl">
                    <div class="h-64 bg-gray-200 flex items-center justify-center">
                        <svg class="w-32 h-32 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-dark">Michael Okonkwo</h3>
                        <p class="text-teal font-medium">Chief Operations Officer</p>
                        <p class="mt-3 text-gray-600">Michael leads our operational excellence initiatives, ensuring efficient execution of our strategic objectives across all markets.</p>
                        <div class="mt-4 flex space-x-3">
                            <a href="#" class="text-gray hover:text-teal transition">
                                <i class="fab fa-linkedin text-lg"></i>
                            </a>
                            <a href="#" class="text-gray hover:text-teal transition">
                                <i class="fab fa-twitter text-lg"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Global Presence Section -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-dark">Global Presence</h2>
                <div class="mt-2 h-1 w-24 bg-teal mx-auto"></div>
                <p class="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">Our strategic investments span across continents, creating value in diverse markets.</p>
            </div>
            
            <div class="relative">
                <div class="h-96 bg-white rounded-lg shadow-md p-4">
                    <svg class="w-full h-full" viewBox="0 0 1000 500" xmlns="http://www.w3.org/2000/svg">
                        <!-- Simplified world map -->
                        <path d="M150,250 Q300,200 450,250 T750,250" fill="none" stroke="#e5e7eb" stroke-width="30" />
                        <path d="M200,150 Q350,100 500,150 T800,150" fill="none" stroke="#e5e7eb" stroke-width="20" />
                        <path d="M250,350 Q400,300 550,350 T850,350" fill="none" stroke="#e5e7eb" stroke-width="25" />
                        
                        <!-- Location markers -->
                        <circle cx="300" cy="200" r="10" fill="#0d9488" />
                        <circle cx="500" cy="150" r="10" fill="#f97316" />
                        <circle cx="700" cy="250" r="10" fill="#0d9488" />
                        <circle cx="400" cy="300" r="10" fill="#f97316" />
                        <circle cx="600" cy="350" r="10" fill="#0d9488" />
                    </svg>
                </div>
                
                <div class="mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="bg-white p-4 rounded-lg shadow-md border-l-4 border-teal">
                        <h3 class="font-semibold text-gray-dark">North America</h3>
                        <p class="text-gray-600 mt-1">Strategic investments in technology, real estate, and financial services sectors.</p>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow-md border-l-4 border-orange">
                        <h3 class="font-semibold text-gray-dark">Europe</h3>
                        <p class="text-gray-600 mt-1">Focused on sustainable energy, infrastructure, and manufacturing ventures.</p>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow-md border-l-4 border-teal">
                        <h3 class="font-semibold text-gray-dark">Asia Pacific</h3>
                        <p class="text-gray-600 mt-1">Expanding presence in emerging markets with high growth potential.</p>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow-md border-l-4 border-orange">
                        <h3 class="font-semibold text-gray-dark">Middle East</h3>
                        <p class="text-gray-600 mt-1">Partnerships in energy, hospitality, and infrastructure development.</p>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow-md border-l-4 border-teal">
                        <h3 class="font-semibold text-gray-dark">Africa</h3>
                        <p class="text-gray-600 mt-1">Investments in natural resources, agriculture, and telecommunications.</p>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow-md border-l-4 border-orange">
                        <h3 class="font-semibold text-gray-dark">South America</h3>
                        <p class="text-gray-600 mt-1">Growing portfolio in renewable energy and agricultural technology.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-teal text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold">Connect With Us</h2>
                <div class="mt-2 h-1 w-24 bg-white mx-auto"></div>
                <p class="mt-4 text-lg opacity-90 max-w-3xl mx-auto">Interested in learning more about Kaiteur International Holdings? We'd love to hear from you.</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-white bg-opacity-10 p-6 rounded-lg backdrop-filter backdrop-blur-sm">
                    <div class="h-12 w-12 rounded-full bg-white bg-opacity-20 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Phone</h3>
                    <p class="opacity-90">+****************</p>
                    <p class="opacity-90">+44 20 7123 4567</p>
                </div>
                
                <div class="bg-white bg-opacity-10 p-6 rounded-lg backdrop-filter backdrop-blur-sm">
                    <div class="h-12 w-12 rounded-full bg-white bg-opacity-20 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Email</h3>
                    <p class="opacity-90"><EMAIL></p>
                    <p class="opacity-90"><EMAIL></p>
                </div>
                
                <div class="bg-white bg-opacity-10 p-6 rounded-lg backdrop-filter backdrop-blur-sm">
                    <div class="h-12 w-12 rounded-full bg-white bg-opacity-20 flex items-center justify-center mb-4">
                        <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Address</h3>
                    <p class="opacity-90">123 Global Avenue</p>
                    <p class="opacity-90">New York, NY 10001</p>
                </div>
            </div>
            
            <div class="mt-12 bg-white bg-opacity-10 p-8 rounded-lg backdrop-filter backdrop-blur-sm">
                <h3 class="text-2xl font-semibold mb-6 text-center">Send Us a Message</h3>
                <form class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium mb-1">Name</label>
                            <input type="text" id="name" class="w-full px-4 py-2 rounded-md bg-white bg-opacity-20 border border-white border-opacity-20 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 text-white placeholder-white placeholder-opacity-70" placeholder="Your name">
                        </div>
                        <div>
                            <label for="email" class="block text-sm font-medium mb-1">Email</label>
                            <input type="email" id="email" class="w-full px-4 py-2 rounded-md bg-white bg-opacity-20 border border-white border-opacity-20 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 text-white placeholder-white placeholder-opacity-70" placeholder="Your email">
                        </div>
                    </div>
                    <div>
                        <label for="subject" class="block text-sm font-medium mb-1">Subject</label>
                        <input type="text" id="subject" class="w-full px-4 py-2 rounded-md bg-white bg-opacity-20 border border-white border-opacity-20 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 text-white placeholder-white placeholder-opacity-70" placeholder="Message subject">
                    </div>
                    <div>
                        <label for="message" class="block text-sm font-medium mb-1">Message</label>
                        <textarea id="message" rows="4" class="w-full px-4 py-2 rounded-md bg-white bg-opacity-20 border border-white border-opacity-20 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 text-white placeholder-white placeholder-opacity-70" placeholder="Your message"></textarea>
                    </div>
                    <div class="text-center">
                        <button type="submit" class="px-6 py-3 bg-white text-teal font-medium rounded-md shadow-md hover:bg-opacity-90 transition duration-300">Send Message</button>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center mb-4">
                        <div class="h-10 w-10 rounded-full bg-teal flex items-center justify-center">
                            <span class="text-white font-bold text-xl">K</span>
                        </div>
                        <span class="ml-2 font-semibold text-lg">Kaiteur International Holdings</span>
                    </div>
                    <p class="text-gray-400">A global leader in strategic investments and business development.</p>
                    <div class="mt-4 flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-teal transition">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-teal transition">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-teal transition">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-teal transition">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-teal transition">Home</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-teal transition">About Us</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-teal transition">Services</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-teal transition">Portfolio</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-teal transition">Contact</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Our Services</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-teal transition">Investment Management</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-teal transition">Business Development</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-teal transition">Strategic Consulting</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-teal transition">Mergers & Acquisitions</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-teal transition">Market Entry Strategy</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Newsletter</h3>
                    <p class="text-gray-400 mb-4">Subscribe to our newsletter for the latest updates and insights.</p>
                    <form class="flex">
                        <input type="email" placeholder="Your email" class="px-4 py-2 rounded-l-md bg-gray-800 border border-gray-700 focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent flex-grow">
                        <button type="submit" class="px-4 py-2 bg-teal text-white rounded-r-md hover:bg-teal-dark transition duration-300">
                            <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                            </svg>
                        </button>
                    </form>
                </div>
            </div>
            
            <div class="mt-12 pt-8 border-t border-gray-800 text-center">
                <p class="text-gray-400">&copy; 2023 Kaiteur International Holdings. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        
        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 80,
                        behavior: 'smooth'
                    });
                    
                    // Close mobile menu if open
                    if (!mobileMenu.classList.contains('hidden')) {
                        mobileMenu.classList.add('hidden');
                    }
                }
            });
        });
        
        // Form submission (demo only)
        const contactForm = document.querySelector('#contact form');
        if (contactForm) {
            contactForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // Get form values
                const name = document.getElementById('name').value;
                const email = document.getElementById('email').value;
                const subject = document.getElementById('subject').value;
                const message = document.getElementById('message').value;
                
                // Simple validation
                if (name && email && subject && message) {
                    // In a real application, you would send this data to a server
                    alert('Thank you for your message! We will get back to you soon.');
                    contactForm.reset();
                } else {
                    alert('Please fill in all fields.');
                }
            });
        }
    </script>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'945f48bf7229a052',t:'MTc0ODI4MzYwMi4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script></body>
</html>