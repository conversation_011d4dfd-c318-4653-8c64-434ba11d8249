'use client'

import { motion } from 'framer-motion'
import { ArrowRight, Globe, TrendingUp, Users } from 'lucide-react'

const Hero = () => {
  const scrollToSection = (href: string) => {
    const element = document.querySelector(href)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  const stats = [
    { 
      icon: Globe, 
      value: '25+', 
      label: 'Years of Excellence',
      iconClass: 'text-black drop-shadow-[0_0_8px_rgba(13,148,136,0.6)]' // Teal shadow
    },
  ]

  return (
    <section id="home" className="relative min-h-screen flex items-center overflow-hidden bg-white">
      {/* Subtle Gradient Background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-teal-50/50 via-white to-orange-50/50"></div>
        <div className="absolute inset-0 bg-[url('/grid-pattern.svg')] opacity-5"></div>
        
        {/* Subtle Gradient Accents */}
        <div className="absolute top-1/4 -left-20 w-64 h-64 bg-teal-100/30 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 -right-20 w-72 h-72 bg-orange-100/30 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="text-gray-800"
          >
            <motion.h1
              className="text-4xl md:text-6xl lg:text-7xl font-bold leading-tight mb-6"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.8 }}
            >
              Empowering Global
              <span className="block gradient-text bg-gradient-to-r from-teal-600 to-orange-500 bg-clip-text text-transparent">
                Humanitarian Investment & Development
              </span>
            </motion.h1>

            <p className="text-xl md:text-2xl text-black mb-8 max-w-2xl">
              Transforming lives through strategic investments in underserved communities, creating sustainable development and economic empowerment worldwide.
            </p>

            <motion.div
              className="flex flex-col sm:flex-row gap-4 mb-12"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.8 }}
            >
              <motion.button
                onClick={() => scrollToSection('#about')}
                className="group px-8 py-4 bg-white/20 backdrop-blur-md text-gray-800 font-semibold rounded-full shadow-xl hover:bg-white/30 hover:shadow-2xl transition-all duration-300 flex items-center justify-center space-x-2 border border-white/30"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                aria-label="Learn more about our company story"
              >
                <span>Our Humanitarian Mission</span>
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" aria-hidden="true" />
              </motion.button>

              <motion.button
                onClick={() => scrollToSection('#contact')}
                className="group px-8 py-4 bg-gradient-to-r from-orange/20 to-gold/20 backdrop-blur-md border-2 border-white/40 text-gray-800 font-semibold rounded-full hover:from-orange/30 hover:to-gold/30 hover:border-white/60 transition-all duration-300 flex items-center justify-center space-x-2"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                aria-label="Contact us for more information"
              >
                Explore Our Impact
                <ArrowRight className="ml-2 w-5 h-5" aria-hidden="true" />
              </motion.button>
            </motion.div>

            {/* Stats */}
            <motion.div
              className="grid grid-cols-1 sm:grid-cols-3 gap-6"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.8 }}
            >
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  className="bg-white/10 backdrop-blur-md border border-white/20 p-4 rounded-xl text-center hover:bg-white/20 transition-all duration-300"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.8 + index * 0.1, duration: 0.5 }}
                  whileHover={{ scale: 1.05, y: -5 }}
                >
                  <stat.icon className={`w-10 h-10 mx-auto mb-3 ${stat.iconClass || 'text-gray-800'}`} />
                  <div className="text-2xl font-bold text-black">{stat.value}</div>
                  <div className="text-sm font-medium text-gray-800">{stat.label}</div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>

          {/* Visual Element */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="relative flex justify-center items-center"
          >
            <div className="relative w-96 h-96">
              {/* Central Circle */}
              <motion.div
                className="absolute inset-0 rounded-full bg-gradient-to-br from-teal-200 to-teal-150 backdrop-blur-sm border border-white/20"
                animate={{ rotate: 360 }}
                transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
              />

              {/* Orbiting Elements */}
              <motion.div
                className="absolute top-8 left-1/2 transform -translate-x-1/2 w-16 h-16 bg-gradient-to-br from-orange to-orange-dark rounded-full flex items-center justify-center shadow-xl"
                animate={{ rotate: -360 }}
                transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
              >
                <Globe className="w-8 h-8 text-white" />
              </motion.div>

              <motion.div
                className="absolute bottom-8 left-1/2 transform -translate-x-1/2 w-16 h-16 bg-gradient-to-br from-gold to-yellow-600 rounded-full flex items-center justify-center shadow-xl"
                animate={{ rotate: -360 }}
                transition={{ duration: 18, repeat: Infinity, ease: "linear" }}
              >
                <TrendingUp className="w-8 h-8 text-white" />
              </motion.div>

              <motion.div
                className="absolute left-8 top-1/2 transform -translate-y-1/2 w-16 h-16 bg-gradient-to-br from-teal-light to-teal rounded-full flex items-center justify-center shadow-xl"
                animate={{ rotate: -360 }}
                transition={{ duration: 12, repeat: Infinity, ease: "linear" }}
              >
                <Users className="w-8 h-8 text-white" />
              </motion.div>

              {/* Center Content */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center text-black">
                  <div className="text-4xl font-bold mb-2">25+</div>
                  <div className="text-sm opacity-80">Years of Excellence</div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.2, duration: 0.8 }}
      >
        <motion.div
          className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center"
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <motion.div
            className="w-1 h-3 bg-white/70 rounded-full mt-2"
            animate={{ y: [0, 6, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
          />
        </motion.div>
      </motion.div>
    </section>
  )
}

export default Hero
