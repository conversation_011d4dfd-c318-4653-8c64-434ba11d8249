'use client'

import { motion } from 'framer-motion'
import { ArrowRight, Heart, Globe, Users, Target } from 'lucide-react'

const Hero = () => {
  const scrollToSection = (href: string) => {
    const element = document.querySelector(href)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  const stats = [
    { icon: Heart, value: '200+', label: 'Humanitarian Projects' },
    { icon: Globe, value: '25+', label: 'Countries Served' },
    { icon: Users, value: '500K+', label: 'Lives Impacted' },
  ]

  return (
    <section id="home" className="relative min-h-screen flex items-center overflow-hidden bg-white">
      {/* Modern Geometric Background */}
      <div className="absolute inset-0">
        {/* Diagonal Split Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-white via-teal-50 to-white"></div>

        {/* Geometric Shapes */}
        <div className="absolute top-0 right-0 w-1/2 h-full bg-gradient-to-bl from-teal-500/5 to-transparent"></div>
        <div className="absolute bottom-0 left-0 w-1/3 h-2/3 bg-gradient-to-tr from-gold-400/5 to-transparent"></div>

        {/* Subtle Grid Pattern */}
        <div className="absolute inset-0 opacity-[0.02]" style={{
          backgroundImage: `linear-gradient(rgba(20, 184, 166, 0.1) 1px, transparent 1px),
                           linear-gradient(90deg, rgba(20, 184, 166, 0.1) 1px, transparent 1px)`,
          backgroundSize: '50px 50px'
        }}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        {/* Top Section - Company Badge */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <div className="inline-flex items-center px-6 py-3 bg-white border-2 border-teal-200 rounded-full shadow-lg">
            <Heart className="w-5 h-5 text-teal-600 mr-2" />
            <span className="text-teal-800 font-semibold text-sm tracking-wide uppercase">Humanitarian Excellence Since 2008</span>
          </div>
        </motion.div>

        {/* Main Content - Centered Layout */}
        <div className="text-center max-w-5xl mx-auto">
          <motion.h1
            className="text-5xl md:text-7xl lg:text-8xl font-black leading-tight mb-8"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.8 }}
          >
            <span className="text-gray-900">Kaiteur</span>
            <br />
            <span className="bg-gradient-to-r from-teal-600 via-gold-500 to-teal-600 bg-clip-text text-transparent">
              Equity Partners
            </span>
          </motion.h1>

          <motion.div
            className="w-32 h-1 bg-gradient-to-r from-teal-500 to-gold-500 mx-auto mb-8"
            initial={{ width: 0 }}
            animate={{ width: 128 }}
            transition={{ delay: 0.6, duration: 0.8 }}
          ></motion.div>

          <motion.p
            className="text-2xl md:text-3xl text-gray-700 mb-12 leading-relaxed font-light"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.8 }}
          >
            Transforming communities through strategic humanitarian project management,
            <br className="hidden md:block" />
            creating sustainable impact across the globe.
          </motion.p>

          {/* CTA Buttons */}
          <motion.div
            className="flex flex-col sm:flex-row gap-6 justify-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.8 }}
          >
            <motion.button
              onClick={() => scrollToSection('#about')}
              className="group px-10 py-5 bg-gradient-to-r from-teal-600 to-teal-700 text-white font-bold rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-300 flex items-center justify-center space-x-3 border-2 border-teal-600"
              whileHover={{ scale: 1.02, y: -3 }}
              whileTap={{ scale: 0.98 }}
              aria-label="Learn more about our humanitarian mission"
            >
              <Heart className="w-6 h-6" />
              <span className="text-lg">Our Mission</span>
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </motion.button>

            <motion.button
              onClick={() => scrollToSection('#contact')}
              className="group px-10 py-5 bg-white border-3 border-gold-500 text-gold-600 font-bold rounded-2xl shadow-xl hover:bg-gold-500 hover:text-white transition-all duration-300 flex items-center justify-center space-x-3"
              whileHover={{ scale: 1.02, y: -3 }}
              whileTap={{ scale: 0.98 }}
              aria-label="Contact us to start your humanitarian project"
            >
              <Target className="w-6 h-6" />
              <span className="text-lg">Start Project</span>
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </motion.button>
          </motion.div>

          {/* Stats Cards */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.0, duration: 0.8 }}
          >
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                className="bg-white border-2 border-gray-100 p-8 rounded-3xl text-center shadow-lg hover:shadow-2xl transition-all duration-300 group"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1.0 + index * 0.2, duration: 0.6 }}
                whileHover={{ y: -8, scale: 1.02 }}
              >
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-teal-500 to-gold-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <stat.icon className="w-8 h-8 text-white" />
                </div>
                <div className="text-4xl font-black text-gray-900 mb-2">{stat.value}</div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>

          {/* Scroll Indicator */}
          <motion.div
            className="absolute bottom-12 left-1/2 transform -translate-x-1/2"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.5, duration: 0.8 }}
          >
            <motion.div
              className="w-8 h-12 border-3 border-teal-400 rounded-full flex justify-center bg-white/50 backdrop-blur-sm"
              animate={{ y: [0, 8, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            >
              <motion.div
                className="w-2 h-4 bg-gradient-to-b from-teal-500 to-gold-500 rounded-full mt-2"
                animate={{ opacity: [1, 0.3, 1] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
              />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default Hero
