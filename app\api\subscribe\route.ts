import { NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

export async function POST(request: Request) {
  try {
    const { email } = await request.json();

    // Validate email
    if (!email || typeof email !== 'string') {
      return NextResponse.json(
        { error: 'Valid email is required' },
        { status: 400 }
      );
    }

    // Email validation regex
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Check environment variables
    if (!process.env.GMAIL_USER || !process.env.GMAIL_APP_PASSWORD) {
      console.error('Missing Gmail credentials in environment variables');
      return NextResponse.json(
        { error: 'Email service not configured' },
        { status: 500 }
      );
    }

    // Create a Nodemailer transporter using Gmail with better configuration
    const transporter = nodemailer.createTransport({
      host: 'smtp.gmail.com',
      port: 587,
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.GMAIL_USER,
        pass: process.env.GMAIL_APP_PASSWORD,
      },
      tls: {
        rejectUnauthorized: false
      }
    });

    // Email options
    const mailOptions = {
      from: `"KIH Newsletter" <${process.env.GMAIL_USER}>`,
      to: '<EMAIL>',
      subject: 'New Newsletter Subscription',
      text: `A new user has subscribed to your newsletter:
      
Email: ${email}

Subscription Date: ${new Date().toLocaleDateString()}
`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #0d9488;">New Newsletter Subscription</h2>
          <p>A new user has subscribed to your newsletter:</p>
          <p><strong>Email:</strong> ${email}</p>
          <p><strong>Subscription Date:</strong> ${new Date().toLocaleDateString()}</p>
        </div>
      `,
    };

    // Send the email
    await transporter.sendMail(mailOptions);

    // Here you might want to store the email in a database as well
    // For example: await saveToDatabase(email);

    return NextResponse.json({ message: 'Subscription successful!' });
  } catch (error) {
    console.error('Error processing subscription:', error);
    return NextResponse.json(
      { error: 'Failed to process subscription' },
      { status: 500 }
    );
  }
}
