const fs = require('fs').promises;
const path = require('path');

const servicePages = [
  'strategic-consulting',
  'mergers-acquisitions',
  'global-market-entry',
  'corporate-finance',
  'project-funding'
];

const updateFile = async (filePath) => {
  try {
    let content = await fs.readFile(filePath, 'utf8');
    
    // Update header gradient to match business development
    content = content.replace(
      /bg-gradient-to-r from-[\w-]+ to-[\w-]+/g,
      'bg-gradient-to-r from-teal-600 to-orange-500'
    );
    
    // Update icon colors
    content = content.replace(
      /text-[\w-]+-200/g,
      'text-teal-100'
    );
    
    // Update text colors
    content = content.replace(
      /text-[\w-]+-100/g,
      'text-white/90'
    );
    
    // Update icon backgrounds
    content = content.replace(
      /bg-[\w-]+-100/g,
      'bg-teal-50'
    );
    
    // Update icon colors
    content = content.replace(
      /text-[\w-]+-600/g,
      'text-teal-600'
    );
    
    await fs.writeFile(filePath, content, 'utf8');
    console.log(`Updated: ${filePath}`);
  } catch (error) {
    console.error(`Error updating ${filePath}:`, error);
  }
};

const updateAllServicePages = async () => {
  const basePath = path.join(process.cwd(), 'app/services');
  
  for (const page of servicePages) {
    const filePath = path.join(basePath, page, 'page.tsx');
    await updateFile(filePath);
  }
  
  console.log('All service pages have been updated to match Business Development page!');
};

updateAllServicePages().catch(console.error);
