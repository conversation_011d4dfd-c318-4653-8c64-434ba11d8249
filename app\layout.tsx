import type { Metadata } from 'next'
import { Montserrat } from 'next/font/google'
import './globals.css'

const montserrat = Montserrat({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700', '800', '900'],
  variable: '--font-montserrat',
})

export const metadata: Metadata = {
  title: 'Kaiteur Equity Partners - Humanitarian Project Management',
  description: 'Leading project management company specializing in humanitarian initiatives, creating sustainable impact across communities worldwide.',
  keywords: 'humanitarian projects, project management, community development, social impact, sustainable development, Kaiteur',
  authors: [{ name: 'Kaiteur Equity Partners' }],
  creator: 'Kaiteur Equity Partners',
  publisher: 'Kaiteur Equity Partners',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://kaiteur-holdings.herokuapp.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: 'Kaiteur International Holdings - Global Investment Leader',
    description: 'A global leader in investment and business development, creating sustainable value across diverse markets worldwide.',
    url: 'https://kaiteur-holdings.herokuapp.com',
    siteName: 'Kaiteur International Holdings',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Kaiteur International Holdings',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Kaiteur International Holdings - Global Investment Leader',
    description: 'A global leader in investment and business development, creating sustainable value across diverse markets worldwide.',
    images: ['/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${montserrat.variable}`}>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#0d9488" />
        <meta name="msapplication-TileColor" content="#0d9488" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
      </head>
      <body className={`${montserrat.className} antialiased`}>
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-teal-50">
          <a href="#main-content" className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-teal text-white px-4 py-2 rounded-md z-50">
            Skip to main content
          </a>
          {children}
        </div>
      </body>
    </html>
  )
}
